<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS UMKM - Kasir</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }
        .cart-item {
            border-bottom: 1px solid #eee;
            padding: 10px 0;
        }
        .cart-item:last-child {
            border-bottom: none;
        }
        .total-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
        }
        .btn-payment {
            background: #28a745;
            border: none;
            border-radius: 10px;
            padding: 15px;
            font-size: 1.1rem;
            font-weight: 600;
        }
        .btn-payment:hover {
            background: #218838;
        }
        .barcode-input {
            font-size: 1.2rem;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #667eea;
        }
        .barcode-input:focus {
            border-color: #764ba2;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .quantity-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .quantity-btn {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: none;
            background: #667eea;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .quantity-input {
            width: 60px;
            text-align: center;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 5px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-cash-register me-2"></i>Kasir POS UMKM
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>
                    <span id="userName">Loading...</span>
                </span>
                <button class="btn btn-outline-light btn-sm me-2" onclick="goToDashboard()">
                    <i class="fas fa-home me-1"></i>Dashboard
                </button>
                <button class="btn btn-outline-light btn-sm" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-1"></i>Keluar
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <div class="row">
            <!-- Left Panel - Product Input -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-barcode me-2"></i>Input Barang</h5>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-8">
                                <input type="text" 
                                       class="form-control barcode-input" 
                                       id="barcodeInput" 
                                       placeholder="Scan barcode atau ketik kode barang..."
                                       autofocus>
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-primary w-100" onclick="addItemManual()">
                                    <i class="fas fa-plus me-2"></i>Tambah Manual
                                </button>
                            </div>
                        </div>
                        
                        <!-- Cart Items -->
                        <div class="card">
                            <div class="card-header">
                                <h6><i class="fas fa-shopping-cart me-2"></i>Keranjang Belanja</h6>
                            </div>
                            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                                <div id="cartItems">
                                    <div class="text-center text-muted py-4">
                                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                                        <p>Keranjang masih kosong</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Panel - Payment -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-calculator me-2"></i>Total & Pembayaran</h5>
                    </div>
                    <div class="card-body">
                        <div class="total-section mb-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span>Subtotal:</span>
                                <span id="subtotal">Rp 0</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Diskon:</span>
                                <span id="diskon">Rp 0</span>
                            </div>
                            <div class="d-flex justify-content-between mb-2">
                                <span>Pajak:</span>
                                <span id="pajak">Rp 0</span>
                            </div>
                            <hr>
                            <div class="d-flex justify-content-between">
                                <strong>TOTAL:</strong>
                                <strong id="total" style="font-size: 1.5rem;">Rp 0</strong>
                            </div>
                        </div>

                        <!-- Payment Methods -->
                        <div class="mb-3">
                            <label class="form-label">Metode Pembayaran:</label>
                            <div class="row">
                                <div class="col-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="cashPayment" onchange="togglePaymentMethod('cash')">
                                        <label class="form-check-label" for="cashPayment">Cash</label>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="cardPayment" onchange="togglePaymentMethod('card')">
                                        <label class="form-check-label" for="cardPayment">Card</label>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="digitalPayment" onchange="togglePaymentMethod('digital')">
                                        <label class="form-check-label" for="digitalPayment">Digital</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Amounts -->
                        <div id="paymentAmounts" style="display: none;">
                            <div id="cashAmount" style="display: none;" class="mb-2">
                                <label class="form-label">Jumlah Cash:</label>
                                <input type="number" class="form-control" id="cashInput" placeholder="0" onchange="calculateChange()">
                            </div>
                            <div id="cardAmount" style="display: none;" class="mb-2">
                                <label class="form-label">Jumlah Card:</label>
                                <input type="number" class="form-control" id="cardInput" placeholder="0" onchange="calculateChange()">
                            </div>
                            <div id="digitalAmount" style="display: none;" class="mb-2">
                                <label class="form-label">Jumlah Digital:</label>
                                <input type="number" class="form-control" id="digitalInput" placeholder="0" onchange="calculateChange()">
                            </div>
                        </div>

                        <!-- Change -->
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <strong>Kembalian:</strong>
                                <strong id="kembalian" class="text-success">Rp 0</strong>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-grid gap-2">
                            <button class="btn btn-success btn-payment" onclick="processPayment()" id="paymentBtn" disabled>
                                <i class="fas fa-credit-card me-2"></i>Proses Pembayaran
                            </button>
                            <button class="btn btn-warning" onclick="clearCart()">
                                <i class="fas fa-trash me-2"></i>Kosongkan Keranjang
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Check if user is logged in
        const user = JSON.parse(sessionStorage.getItem('user') || '{}');
        if (!user.username) {
            window.location.href = window.location.origin + window.location.pathname + '?page=login';
        }

        // Display user name
        document.getElementById('userName').textContent = user.nama_lengkap || user.username;

        // Cart data
        let cart = [];
        let paymentMethods = [];

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('barcodeInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    addItemByBarcode();
                }
            });
        });

        function goToDashboard() {
            window.location.href = window.location.origin + window.location.pathname + '?page=dashboard';
        }

        function logout() {
            sessionStorage.removeItem('user');
            window.location.href = window.location.origin + window.location.pathname + '?page=login';
        }

        async function addItemByBarcode() {
            const barcode = document.getElementById('barcodeInput').value.trim();
            if (!barcode) return;

            try {
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'getBarangByBarcode',
                        barcode: barcode
                    })
                });

                const barang = await response.json();
                if (barang) {
                    addItemToCart(barang);
                    document.getElementById('barcodeInput').value = '';
                } else {
                    alert('Barang tidak ditemukan!');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Terjadi kesalahan saat mencari barang');
            }
        }

        function addItemToCart(barang, quantity = 1) {
            const existingIndex = cart.findIndex(item => item.barang_id === barang.barang_id);

            if (existingIndex >= 0) {
                cart[existingIndex].quantity += quantity;
                cart[existingIndex].subtotal_item = cart[existingIndex].quantity * cart[existingIndex].harga_jual;
            } else {
                cart.push({
                    barang_id: barang.barang_id,
                    nama_barang: barang.nama_barang,
                    harga_jual: barang.harga_jual,
                    harga_beli: barang.harga_beli,
                    quantity: quantity,
                    subtotal_item: quantity * barang.harga_jual,
                    diskon_item_persen: 0,
                    diskon_item_nominal: 0
                });
            }

            updateCartDisplay();
            updateTotals();
        }

        function updateCartDisplay() {
            const cartContainer = document.getElementById('cartItems');

            if (cart.length === 0) {
                cartContainer.innerHTML = `
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <p>Keranjang masih kosong</p>
                    </div>
                `;
                return;
            }

            cartContainer.innerHTML = cart.map((item, index) => `
                <div class="cart-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1">
                            <strong>${item.nama_barang}</strong><br>
                            <small class="text-muted">Rp ${formatCurrency(item.harga_jual)}</small>
                        </div>
                        <div class="quantity-control">
                            <button class="quantity-btn" onclick="changeQuantity(${index}, -1)">-</button>
                            <input type="number" class="quantity-input" value="${item.quantity}"
                                   onchange="setQuantity(${index}, this.value)" min="1">
                            <button class="quantity-btn" onclick="changeQuantity(${index}, 1)">+</button>
                        </div>
                        <div class="text-end ms-3">
                            <strong>Rp ${formatCurrency(item.subtotal_item)}</strong><br>
                            <button class="btn btn-sm btn-danger" onclick="removeItem(${index})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function changeQuantity(index, change) {
            cart[index].quantity += change;
            if (cart[index].quantity <= 0) {
                cart.splice(index, 1);
            } else {
                cart[index].subtotal_item = cart[index].quantity * cart[index].harga_jual;
            }
            updateCartDisplay();
            updateTotals();
        }

        function setQuantity(index, quantity) {
            const qty = parseInt(quantity) || 1;
            cart[index].quantity = qty;
            cart[index].subtotal_item = qty * cart[index].harga_jual;
            updateCartDisplay();
            updateTotals();
        }

        function removeItem(index) {
            cart.splice(index, 1);
            updateCartDisplay();
            updateTotals();
        }

        function updateTotals() {
            const subtotal = cart.reduce((sum, item) => sum + item.subtotal_item, 0);
            const diskon = 0; // Can be implemented later
            const pajak = 0; // Can be implemented later
            const total = subtotal - diskon + pajak;

            document.getElementById('subtotal').textContent = 'Rp ' + formatCurrency(subtotal);
            document.getElementById('diskon').textContent = 'Rp ' + formatCurrency(diskon);
            document.getElementById('pajak').textContent = 'Rp ' + formatCurrency(pajak);
            document.getElementById('total').textContent = 'Rp ' + formatCurrency(total);

            calculateChange();
        }

        function togglePaymentMethod(method) {
            const checkbox = document.getElementById(method + 'Payment');
            const amountDiv = document.getElementById(method + 'Amount');
            const paymentAmountsDiv = document.getElementById('paymentAmounts');

            if (checkbox.checked) {
                paymentMethods.push(method);
                amountDiv.style.display = 'block';
            } else {
                paymentMethods = paymentMethods.filter(m => m !== method);
                amountDiv.style.display = 'none';
                document.getElementById(method + 'Input').value = '';
            }

            paymentAmountsDiv.style.display = paymentMethods.length > 0 ? 'block' : 'none';
            calculateChange();
        }

        function calculateChange() {
            const total = cart.reduce((sum, item) => sum + item.subtotal_item, 0);
            const cashAmount = parseFloat(document.getElementById('cashInput').value) || 0;
            const cardAmount = parseFloat(document.getElementById('cardInput').value) || 0;
            const digitalAmount = parseFloat(document.getElementById('digitalInput').value) || 0;

            const totalPaid = cashAmount + cardAmount + digitalAmount;
            const change = totalPaid - total;

            document.getElementById('kembalian').textContent = 'Rp ' + formatCurrency(Math.max(0, change));

            // Enable payment button if total is paid
            const paymentBtn = document.getElementById('paymentBtn');
            paymentBtn.disabled = totalPaid < total || cart.length === 0;
        }

        function clearCart() {
            if (confirm('Yakin ingin mengosongkan keranjang?')) {
                cart = [];
                updateCartDisplay();
                updateTotals();
            }
        }

        async function processPayment() {
            if (cart.length === 0) {
                alert('Keranjang masih kosong!');
                return;
            }

            const total = cart.reduce((sum, item) => sum + item.subtotal_item, 0);
            const cashAmount = parseFloat(document.getElementById('cashInput').value) || 0;
            const cardAmount = parseFloat(document.getElementById('cardInput').value) || 0;
            const digitalAmount = parseFloat(document.getElementById('digitalInput').value) || 0;
            const totalPaid = cashAmount + cardAmount + digitalAmount;

            if (totalPaid < total) {
                alert('Jumlah pembayaran kurang!');
                return;
            }

            const transaksiData = {
                user_id: user.user_id,
                pelanggan_id: '',
                subtotal: total,
                diskon_persen: 0,
                diskon_nominal: 0,
                pajak_persen: 0,
                pajak_nominal: 0,
                total_bayar: total,
                metode_pembayaran_id: 1, // Default
                jumlah_cash: cashAmount,
                jumlah_card: cardAmount,
                jumlah_digital: digitalAmount,
                kembalian: totalPaid - total,
                catatan: '',
                items: cart
            };

            try {
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        action: 'createPenjualan',
                        transaksi: transaksiData
                    })
                });

                const result = await response.json();
                if (result.success) {
                    alert('Transaksi berhasil! Nomor: ' + result.nomor_transaksi);
                    printReceipt(result);
                    clearCart();
                    resetPayment();
                } else {
                    alert('Error: ' + result.message);
                }
            } catch (error) {
                console.error('Error:', error);
                alert('Terjadi kesalahan saat memproses pembayaran');
            }
        }

        function resetPayment() {
            paymentMethods = [];
            document.getElementById('cashPayment').checked = false;
            document.getElementById('cardPayment').checked = false;
            document.getElementById('digitalPayment').checked = false;
            document.getElementById('paymentAmounts').style.display = 'none';
            document.getElementById('cashAmount').style.display = 'none';
            document.getElementById('cardAmount').style.display = 'none';
            document.getElementById('digitalAmount').style.display = 'none';
            document.getElementById('cashInput').value = '';
            document.getElementById('cardInput').value = '';
            document.getElementById('digitalInput').value = '';
        }

        function printReceipt(result) {
            // Simple print functionality
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <html>
                <head><title>Struk Penjualan</title></head>
                <body style="font-family: monospace; font-size: 12px;">
                    <div style="text-align: center;">
                        <h3>TOKO UMKM</h3>
                        <p>Struk Penjualan</p>
                        <p>No: ${result.nomor_transaksi}</p>
                        <p>Tanggal: ${new Date().toLocaleDateString('id-ID')}</p>
                        <hr>
                    </div>
                    ${cart.map(item => `
                        <div style="display: flex; justify-content: space-between;">
                            <span>${item.nama_barang}</span>
                            <span>Rp ${formatCurrency(item.subtotal_item)}</span>
                        </div>
                        <div style="margin-left: 20px; font-size: 10px;">
                            ${item.quantity} x Rp ${formatCurrency(item.harga_jual)}
                        </div>
                    `).join('')}
                    <hr>
                    <div style="display: flex; justify-content: space-between;">
                        <strong>TOTAL: Rp ${formatCurrency(cart.reduce((sum, item) => sum + item.subtotal_item, 0))}</strong>
                    </div>
                    <div style="text-align: center; margin-top: 20px;">
                        <p>Terima Kasih!</p>
                    </div>
                </body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('id-ID').format(amount);
        }

        function addItemManual() {
            alert('Fitur tambah manual akan segera tersedia');
        }
    </script>
</body>
</html>
