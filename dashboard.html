<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS UMKM - Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        .menu-card {
            cursor: pointer;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
        }
        .menu-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #667eea;
        }
        .menu-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-cash-register me-2"></i>POS UMKM
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>
                    <span id="userName">Loading...</span>
                </span>
                <button class="btn btn-outline-light btn-sm" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-1"></i>Keluar
                </button>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <h2>Selamat Datang di Dashboard POS UMKM</h2>
                <p class="text-muted">Kelola toko Anda dengan mudah dan efisien</p>
            </div>
        </div>

        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                        <div class="stats-number" id="totalPenjualan">0</div>
                        <div>Total Penjualan Hari Ini</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-box fa-2x mb-2"></i>
                        <div class="stats-number" id="totalBarang">0</div>
                        <div>Total Barang</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-users fa-2x mb-2"></i>
                        <div class="stats-number" id="totalPelanggan">0</div>
                        <div>Total Pelanggan</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                        <div class="stats-number" id="omzetHariIni">Rp 0</div>
                        <div>Omzet Hari Ini</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Menu Cards -->
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card menu-card" onclick="goToKasir()">
                    <i class="fas fa-cash-register menu-icon"></i>
                    <div class="menu-title">Kasir</div>
                    <small class="text-muted">Proses transaksi penjualan</small>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card menu-card" onclick="openModal('barangModal')">
                    <i class="fas fa-box menu-icon"></i>
                    <div class="menu-title">Kelola Barang</div>
                    <small class="text-muted">Tambah, edit, hapus barang</small>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card menu-card" onclick="openModal('kategoriModal')">
                    <i class="fas fa-tags menu-icon"></i>
                    <div class="menu-title">Kelola Kategori</div>
                    <small class="text-muted">Atur kategori barang</small>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card menu-card" onclick="openModal('satuanModal')">
                    <i class="fas fa-balance-scale menu-icon"></i>
                    <div class="menu-title">Kelola Satuan</div>
                    <small class="text-muted">Atur satuan barang</small>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card menu-card" onclick="openModal('pelangganModal')">
                    <i class="fas fa-users menu-icon"></i>
                    <div class="menu-title">Kelola Pelanggan</div>
                    <small class="text-muted">Data pelanggan</small>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card menu-card" onclick="openModal('laporanModal')">
                    <i class="fas fa-chart-bar menu-icon"></i>
                    <div class="menu-title">Laporan</div>
                    <small class="text-muted">Laporan penjualan</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals will be added here -->
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Check if user is logged in
        const user = JSON.parse(sessionStorage.getItem('user') || '{}');
        if (!user.username) {
            window.location.href = window.location.origin + window.location.pathname + '?page=login';
        }

        // Display user name
        document.getElementById('userName').textContent = user.nama_lengkap || user.username;

        // Load dashboard stats
        async function loadStats() {
            // This would typically load from your backend
            // For now, showing placeholder data
            document.getElementById('totalPenjualan').textContent = '0';
            document.getElementById('totalBarang').textContent = '0';
            document.getElementById('totalPelanggan').textContent = '0';
            document.getElementById('omzetHariIni').textContent = 'Rp 0';
        }

        function goToKasir() {
            window.location.href = window.location.origin + window.location.pathname + '?page=kasir';
        }

        function openModal(modalId) {
            // This will be implemented when we create the modals
            alert('Fitur ' + modalId + ' akan segera tersedia');
        }

        function logout() {
            sessionStorage.removeItem('user');
            window.location.href = window.location.origin + window.location.pathname + '?page=login';
        }

        // Load stats on page load
        loadStats();
    </script>
</body>
</html>
