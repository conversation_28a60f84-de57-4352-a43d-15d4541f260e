# POS UMKM - Point of Sale System

Aplikasi Point of Sale (POS) berbasis Google Apps Script untuk Usaha <PERSON> (UMKM).

## Fitur Utama

### ✅ Sudah Tersedia
- **Authentication System**: Login sederhana untuk kasir dan administrator
- **Transaksi Penjualan**: Sistem kasir dengan barcode scanner support
- **Multiple Payment Methods**: Cash, Card, dan Digital Wallet (bisa kombinasi)
- **Database Management**: Menggunakan Google Sheets sebagai database
- **Auto Increment ID**: Sistem ID otomatis untuk semua tabel
- **Inventory Management**: Update stok otomatis saat transaksi
- **Print Receipt**: Cetak struk menggunakan browser
- **Responsive Design**: Interface yang mobile-friendly

### 🚧 Dalam Pengembangan
- CRUD untuk semua master data (Barang, Kategori, Satuan, Pelanggan)
- Laporan penjualan
- Dashboard analytics
- Manajemen user

## Struktur Database (Google Sheets)

### Sheet: `users`
| Kolom | Tipe | Keterangan |
|-------|------|------------|
| A: user_id | Auto Increment | Primary Key |
| B: username | Text | Unique |
| C: password | Text | Plain text |
| D: nama_lengkap | Text | Nama lengkap user |
| E: role | Text | kasir/administrator |
| F: keterangan | Text | Keterangan tambahan |

### Sheet: `barang`
| Kolom | Tipe | Keterangan |
|-------|------|------------|
| A: barang_id | Auto Increment | Primary Key |
| B: barcode | Text | Barcode/SKU |
| C: nama_barang | Text | Nama barang |
| D: kategori_id | Number | Foreign Key ke kategori |
| E: satuan_id | Number | Foreign Key ke satuan |
| F: harga_beli | Number | Harga beli |
| G: harga_jual | Number | Harga jual |
| H: stok | Number | Jumlah stok |
| I: keterangan | Text | Keterangan |

### Sheet: `kategori`
| Kolom | Tipe | Keterangan |
|-------|------|------------|
| A: kategori_id | Auto Increment | Primary Key |
| B: nama_kategori | Text | Nama kategori |
| C: keterangan | Text | Keterangan |

### Sheet: `satuan`
| Kolom | Tipe | Keterangan |
|-------|------|------------|
| A: satuan_id | Auto Increment | Primary Key |
| B: nama_satuan | Text | Nama satuan (pcs, kg, dll) |
| C: keterangan | Text | Keterangan |

### Sheet: `penjualan`
| Kolom | Tipe | Keterangan |
|-------|------|------------|
| A: penjualan_id | Auto Increment | Primary Key |
| B: nomor_transaksi | Text | Nomor transaksi unik |
| C: tanggal_transaksi | Date | Tanggal transaksi |
| D: user_id | Number | Foreign Key ke users |
| E: pelanggan_id | Number | Foreign Key ke pelanggan |
| F: subtotal | Number | Subtotal sebelum diskon/pajak |
| G: diskon_persen | Number | Diskon dalam persen |
| H: diskon_nominal | Number | Diskon dalam nominal |
| I: pajak_persen | Number | Pajak dalam persen |
| J: pajak_nominal | Number | Pajak dalam nominal |
| K: total_bayar | Number | Total yang harus dibayar |
| L: metode_pembayaran_id | Number | Foreign Key ke metode_pembayaran |
| M: jumlah_cash | Number | Jumlah pembayaran cash |
| N: jumlah_card | Number | Jumlah pembayaran card |
| O: jumlah_digital | Number | Jumlah pembayaran digital |
| P: kembalian | Number | Kembalian |
| Q: catatan | Text | Catatan transaksi |
| R: jam | Time | Jam transaksi |
| S: tanggal | Date | Tanggal (duplikat untuk keperluan tertentu) |

### Sheet: `penjualan_item`
| Kolom | Tipe | Keterangan |
|-------|------|------------|
| A: penjualan_item_id | Auto Increment | Primary Key |
| B: penjualan_id | Number | Foreign Key ke penjualan |
| C: barang_id | Number | Foreign Key ke barang |
| D: harga_beli | Number | Harga beli saat transaksi |
| E: harga_jual | Number | Harga jual saat transaksi |
| F: quantity | Number | Jumlah barang |
| G: diskon_item_persen | Number | Diskon item dalam persen |
| H: diskon_item_nominal | Number | Diskon item dalam nominal |
| I: subtotal_item | Number | Subtotal item |

### Sheet: `pelanggan`
| Kolom | Tipe | Keterangan |
|-------|------|------------|
| A: pelanggan_id | Auto Increment | Primary Key |
| B: nama_pelanggan | Text | Nama pelanggan |
| C: telepon | Text | Nomor telepon |
| D: email | Text | Email |
| E: alamat | Text | Alamat |
| F: keterangan | Text | Keterangan |

### Sheet: `metode_pembayaran`
| Kolom | Tipe | Keterangan |
|-------|------|------------|
| A: metode_pembayaran_id | Auto Increment | Primary Key |
| B: nama_metode_pembayaran | Text | Nama metode pembayaran |
| C: keterangan | Text | Keterangan |

## Setup dan Instalasi

### 1. Persiapan Google Sheets
1. Buat Google Sheets baru dengan nama `pos`
2. Buat 8 sheet dengan nama sesuai struktur database di atas
3. Tambahkan header kolom sesuai dengan struktur yang telah ditentukan

### 2. Setup Google Apps Script
1. Buka [Google Apps Script](https://script.google.com)
2. Buat project baru
3. Copy semua kode dari `Code.gs` ke dalam project
4. Buat file HTML baru untuk setiap file HTML (login.html, dashboard.html, kasir.html)
5. Copy kode HTML ke masing-masing file

### 3. Deploy Web App
1. Di Google Apps Script, klik **Deploy** > **New deployment**
2. Pilih type: **Web app**
3. Execute as: **Me**
4. Who has access: **Anyone** (atau sesuai kebutuhan)
5. Klik **Deploy**
6. Copy URL web app yang dihasilkan

### 4. Setup Data Awal
1. Di Google Apps Script, jalankan fungsi `setupInitialData()` untuk membuat data awal
2. Data awal yang dibuat:
   - User admin: username `admin`, password `admin123`
   - User kasir: username `kasir`, password `kasir123`
   - Metode pembayaran: Cash, Card, Digital Wallet

### 5. Testing
1. Akses URL web app
2. Login menggunakan akun demo
3. Test fitur kasir dengan menambahkan data barang terlebih dahulu

## Penggunaan

### Login
- Akses URL web app
- Masukkan username dan password
- Klik **Masuk**

### Kasir
1. Dari dashboard, klik **Kasir**
2. Scan barcode atau ketik kode barang
3. Atur quantity jika diperlukan
4. Pilih metode pembayaran
5. Masukkan jumlah pembayaran
6. Klik **Proses Pembayaran**
7. Cetak struk jika diperlukan

### Dashboard
- Lihat statistik penjualan
- Akses menu CRUD untuk master data
- Lihat laporan (coming soon)

## Teknologi yang Digunakan

- **Backend**: Google Apps Script (JavaScript)
- **Database**: Google Sheets
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Icons**: Font Awesome 6

## Kontribusi

Silakan buat issue atau pull request untuk perbaikan dan penambahan fitur.

## Lisensi

MIT License - Silakan gunakan untuk keperluan komersial maupun non-komersial.
