// ===== KONFIGURASI APLIKASI =====
const SPREADSHEET_NAME = 'pos';
const SHEETS = {
  USERS: 'users',
  BARANG: 'barang',
  KATEGORI: 'kategori',
  SATUAN: 'satuan',
  PENJUALAN: 'penjualan',
  PENJUALAN_ITEM: 'penjualan_item',
  PELANGGAN: 'pelanggan',
  METODE_PEMBAYARAN: 'metode_pembayaran'
};

// ===== DATABASE HELPER FUNCTIONS =====

/**
 * Mendapatkan spreadsheet POS
 */
function getSpreadsheet() {
  try {
    const files = DriveApp.getFilesByName(SPREADSHEET_NAME);
    if (files.hasNext()) {
      const file = files.next();
      return SpreadsheetApp.openById(file.getId());
    }
    throw new Error(`Spreadsheet '${SPREADSHEET_NAME}' tidak ditemukan`);
  } catch (error) {
    console.error('Error getting spreadsheet:', error);
    throw error;
  }
}

/**
 * Mendapatkan sheet berdasarkan nama
 */
function getSheet(sheetName) {
  try {
    const spreadsheet = getSpreadsheet();
    const sheet = spreadsheet.getSheetByName(sheetName);
    if (!sheet) {
      throw new Error(`Sheet '${sheetName}' tidak ditemukan`);
    }
    return sheet;
  } catch (error) {
    console.error(`Error getting sheet ${sheetName}:`, error);
    throw error;
  }
}

/**
 * Generate auto increment ID untuk sheet tertentu
 */
function getNextId(sheetName, idColumn = 'A') {
  try {
    const sheet = getSheet(sheetName);
    const lastRow = sheet.getLastRow();

    if (lastRow <= 1) {
      return 1; // Jika hanya ada header atau kosong
    }

    const range = sheet.getRange(`${idColumn}2:${idColumn}${lastRow}`);
    const values = range.getValues().flat();
    const maxId = Math.max(...values.filter(val => !isNaN(val) && val !== ''));

    return maxId + 1;
  } catch (error) {
    console.error(`Error getting next ID for ${sheetName}:`, error);
    return 1;
  }
}

/**
 * Mendapatkan semua data dari sheet
 */
function getAllData(sheetName) {
  try {
    const sheet = getSheet(sheetName);
    const lastRow = sheet.getLastRow();
    const lastCol = sheet.getLastColumn();

    if (lastRow <= 1) {
      return [];
    }

    const headers = sheet.getRange(1, 1, 1, lastCol).getValues()[0];
    const data = sheet.getRange(2, 1, lastRow - 1, lastCol).getValues();

    return data.map(row => {
      const obj = {};
      headers.forEach((header, index) => {
        obj[header] = row[index];
      });
      return obj;
    });
  } catch (error) {
    console.error(`Error getting all data from ${sheetName}:`, error);
    return [];
  }
}

/**
 * Mencari data berdasarkan kondisi
 */
function findData(sheetName, condition) {
  try {
    const allData = getAllData(sheetName);
    return allData.filter(row => {
      return Object.keys(condition).every(key => {
        return row[key] == condition[key];
      });
    });
  } catch (error) {
    console.error(`Error finding data in ${sheetName}:`, error);
    return [];
  }
}

/**
 * Menambah data baru ke sheet
 */
function insertData(sheetName, data) {
  try {
    const sheet = getSheet(sheetName);
    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];

    const row = headers.map(header => data[header] || '');
    sheet.appendRow(row);

    return { success: true, message: 'Data berhasil ditambahkan' };
  } catch (error) {
    console.error(`Error inserting data to ${sheetName}:`, error);
    return { success: false, message: error.message };
  }
}

/**
 * Update data berdasarkan ID
 */
function updateData(sheetName, id, data, idColumn = 'A') {
  try {
    const sheet = getSheet(sheetName);
    const lastRow = sheet.getLastRow();
    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];

    // Cari baris dengan ID yang sesuai
    for (let i = 2; i <= lastRow; i++) {
      const cellValue = sheet.getRange(i, 1).getValue();
      if (cellValue == id) {
        // Update data
        headers.forEach((header, index) => {
          if (data.hasOwnProperty(header) && header !== headers[0]) { // Skip ID column
            sheet.getRange(i, index + 1).setValue(data[header]);
          }
        });
        return { success: true, message: 'Data berhasil diupdate' };
      }
    }

    return { success: false, message: 'Data tidak ditemukan' };
  } catch (error) {
    console.error(`Error updating data in ${sheetName}:`, error);
    return { success: false, message: error.message };
  }
}

/**
 * Hapus data berdasarkan ID
 */
function deleteData(sheetName, id, idColumn = 'A') {
  try {
    const sheet = getSheet(sheetName);
    const lastRow = sheet.getLastRow();

    // Cari baris dengan ID yang sesuai
    for (let i = 2; i <= lastRow; i++) {
      const cellValue = sheet.getRange(i, 1).getValue();
      if (cellValue == id) {
        sheet.deleteRow(i);
        return { success: true, message: 'Data berhasil dihapus' };
      }
    }

    return { success: false, message: 'Data tidak ditemukan' };
  } catch (error) {
    console.error(`Error deleting data from ${sheetName}:`, error);
    return { success: false, message: error.message };
  }
}