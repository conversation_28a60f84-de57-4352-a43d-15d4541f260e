// ===== KONFIGURASI APLIKASI =====
const SPREADSHEET_NAME = 'pos';
const SHEETS = {
  USERS: 'users',
  BARANG: 'barang',
  KATEGORI: 'kategori',
  SATUAN: 'satuan',
  PENJUALAN: 'penjualan',
  PENJUALAN_ITEM: 'penjualan_item',
  PELANGGAN: 'pelanggan',
  METODE_PEMBAYARAN: 'metode_pembayaran'
};

// ===== DATABASE HELPER FUNCTIONS =====

/**
 * Mendapatkan spreadsheet POS
 */
function getSpreadsheet() {
  try {
    const files = DriveApp.getFilesByName(SPREADSHEET_NAME);
    if (files.hasNext()) {
      const file = files.next();
      return SpreadsheetApp.openById(file.getId());
    }
    throw new Error(`Spreadsheet '${SPREADSHEET_NAME}' tidak ditemukan`);
  } catch (error) {
    console.error('Error getting spreadsheet:', error);
    throw error;
  }
}

/**
 * Mendapatkan sheet berdasarkan nama
 */
function getSheet(sheetName) {
  try {
    const spreadsheet = getSpreadsheet();
    const sheet = spreadsheet.getSheetByName(sheetName);
    if (!sheet) {
      throw new Error(`Sheet '${sheetName}' tidak ditemukan`);
    }
    return sheet;
  } catch (error) {
    console.error(`Error getting sheet ${sheetName}:`, error);
    throw error;
  }
}

/**
 * Generate auto increment ID untuk sheet tertentu
 */
function getNextId(sheetName, idColumn = 'A') {
  try {
    const sheet = getSheet(sheetName);
    const lastRow = sheet.getLastRow();

    if (lastRow <= 1) {
      return 1; // Jika hanya ada header atau kosong
    }

    const range = sheet.getRange(`${idColumn}2:${idColumn}${lastRow}`);
    const values = range.getValues().flat();
    const maxId = Math.max(...values.filter(val => !isNaN(val) && val !== ''));

    return maxId + 1;
  } catch (error) {
    console.error(`Error getting next ID for ${sheetName}:`, error);
    return 1;
  }
}

/**
 * Mendapatkan semua data dari sheet
 */
function getAllData(sheetName) {
  try {
    const sheet = getSheet(sheetName);
    const lastRow = sheet.getLastRow();
    const lastCol = sheet.getLastColumn();

    if (lastRow <= 1) {
      return [];
    }

    const headers = sheet.getRange(1, 1, 1, lastCol).getValues()[0];
    const data = sheet.getRange(2, 1, lastRow - 1, lastCol).getValues();

    return data.map(row => {
      const obj = {};
      headers.forEach((header, index) => {
        obj[header] = row[index];
      });
      return obj;
    });
  } catch (error) {
    console.error(`Error getting all data from ${sheetName}:`, error);
    return [];
  }
}

/**
 * Mencari data berdasarkan kondisi
 */
function findData(sheetName, condition) {
  try {
    const allData = getAllData(sheetName);
    return allData.filter(row => {
      return Object.keys(condition).every(key => {
        return row[key] == condition[key];
      });
    });
  } catch (error) {
    console.error(`Error finding data in ${sheetName}:`, error);
    return [];
  }
}

/**
 * Menambah data baru ke sheet
 */
function insertData(sheetName, data) {
  try {
    const sheet = getSheet(sheetName);
    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];

    const row = headers.map(header => data[header] || '');
    sheet.appendRow(row);

    return { success: true, message: 'Data berhasil ditambahkan' };
  } catch (error) {
    console.error(`Error inserting data to ${sheetName}:`, error);
    return { success: false, message: error.message };
  }
}

/**
 * Update data berdasarkan ID
 */
function updateData(sheetName, id, data, idColumn = 'A') {
  try {
    const sheet = getSheet(sheetName);
    const lastRow = sheet.getLastRow();
    const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];

    // Cari baris dengan ID yang sesuai
    for (let i = 2; i <= lastRow; i++) {
      const cellValue = sheet.getRange(i, 1).getValue();
      if (cellValue == id) {
        // Update data
        headers.forEach((header, index) => {
          if (data.hasOwnProperty(header) && header !== headers[0]) { // Skip ID column
            sheet.getRange(i, index + 1).setValue(data[header]);
          }
        });
        return { success: true, message: 'Data berhasil diupdate' };
      }
    }

    return { success: false, message: 'Data tidak ditemukan' };
  } catch (error) {
    console.error(`Error updating data in ${sheetName}:`, error);
    return { success: false, message: error.message };
  }
}

/**
 * Hapus data berdasarkan ID
 */
function deleteData(sheetName, id, idColumn = 'A') {
  try {
    const sheet = getSheet(sheetName);
    const lastRow = sheet.getLastRow();

    // Cari baris dengan ID yang sesuai
    for (let i = 2; i <= lastRow; i++) {
      const cellValue = sheet.getRange(i, 1).getValue();
      if (cellValue == id) {
        sheet.deleteRow(i);
        return { success: true, message: 'Data berhasil dihapus' };
      }
    }

    return { success: false, message: 'Data tidak ditemukan' };
  } catch (error) {
    console.error(`Error deleting data from ${sheetName}:`, error);
    return { success: false, message: error.message };
  }
}

// ===== AUTHENTICATION SYSTEM =====

/**
 * Verifikasi login user
 */
function verifyLogin(username, password) {
  try {
    const users = findData(SHEETS.USERS, { username: username, password: password });

    if (users.length > 0) {
      const user = users[0];
      return {
        success: true,
        user: {
          user_id: user.user_id,
          username: user.username,
          nama_lengkap: user.nama_lengkap,
          role: user.role
        }
      };
    }

    return { success: false, message: 'Username atau password salah' };
  } catch (error) {
    console.error('Error verifying login:', error);
    return { success: false, message: 'Terjadi kesalahan sistem' };
  }
}

/**
 * Membuat user baru
 */
function createUser(userData) {
  try {
    // Cek apakah username sudah ada
    const existingUsers = findData(SHEETS.USERS, { username: userData.username });
    if (existingUsers.length > 0) {
      return { success: false, message: 'Username sudah digunakan' };
    }

    const newUser = {
      user_id: getNextId(SHEETS.USERS),
      username: userData.username,
      password: userData.password,
      nama_lengkap: userData.nama_lengkap,
      role: userData.role || 'kasir',
      keterangan: userData.keterangan || ''
    };

    return insertData(SHEETS.USERS, newUser);
  } catch (error) {
    console.error('Error creating user:', error);
    return { success: false, message: error.message };
  }
}

// ===== CRUD OPERATIONS - KATEGORI =====

function getAllKategori() {
  return getAllData(SHEETS.KATEGORI);
}

function createKategori(data) {
  const newKategori = {
    kategori_id: getNextId(SHEETS.KATEGORI),
    nama_kategori: data.nama_kategori,
    keterangan: data.keterangan || ''
  };
  return insertData(SHEETS.KATEGORI, newKategori);
}

function updateKategori(id, data) {
  return updateData(SHEETS.KATEGORI, id, data);
}

function deleteKategori(id) {
  return deleteData(SHEETS.KATEGORI, id);
}

// ===== CRUD OPERATIONS - SATUAN =====

function getAllSatuan() {
  return getAllData(SHEETS.SATUAN);
}

function createSatuan(data) {
  const newSatuan = {
    satuan_id: getNextId(SHEETS.SATUAN),
    nama_satuan: data.nama_satuan,
    keterangan: data.keterangan || ''
  };
  return insertData(SHEETS.SATUAN, newSatuan);
}

function updateSatuan(id, data) {
  return updateData(SHEETS.SATUAN, id, data);
}

function deleteSatuan(id) {
  return deleteData(SHEETS.SATUAN, id);
}

// ===== CRUD OPERATIONS - BARANG =====

function getAllBarang() {
  return getAllData(SHEETS.BARANG);
}

function getBarangByBarcode(barcode) {
  const results = findData(SHEETS.BARANG, { barcode: barcode });
  return results.length > 0 ? results[0] : null;
}

function createBarang(data) {
  const newBarang = {
    barang_id: getNextId(SHEETS.BARANG),
    barcode: data.barcode,
    nama_barang: data.nama_barang,
    kategori_id: data.kategori_id,
    satuan_id: data.satuan_id,
    harga_beli: data.harga_beli,
    harga_jual: data.harga_jual,
    stok: data.stok || 0,
    keterangan: data.keterangan || ''
  };
  return insertData(SHEETS.BARANG, newBarang);
}

function updateBarang(id, data) {
  return updateData(SHEETS.BARANG, id, data);
}

function deleteBarang(id) {
  return deleteData(SHEETS.BARANG, id);
}

function updateStokBarang(barangId, quantity) {
  try {
    const barang = findData(SHEETS.BARANG, { barang_id: barangId });
    if (barang.length === 0) {
      return { success: false, message: 'Barang tidak ditemukan' };
    }

    const currentStok = parseInt(barang[0].stok) || 0;
    const newStok = currentStok - quantity;

    if (newStok < 0) {
      return { success: false, message: 'Stok tidak mencukupi' };
    }

    return updateData(SHEETS.BARANG, barangId, { stok: newStok });
  } catch (error) {
    console.error('Error updating stok:', error);
    return { success: false, message: error.message };
  }
}

// ===== CRUD OPERATIONS - PELANGGAN =====

function getAllPelanggan() {
  return getAllData(SHEETS.PELANGGAN);
}

function createPelanggan(data) {
  const newPelanggan = {
    pelanggan_id: getNextId(SHEETS.PELANGGAN),
    nama_pelanggan: data.nama_pelanggan,
    telepon: data.telepon || '',
    email: data.email || '',
    alamat: data.alamat || '',
    keterangan: data.keterangan || ''
  };
  return insertData(SHEETS.PELANGGAN, newPelanggan);
}

function updatePelanggan(id, data) {
  return updateData(SHEETS.PELANGGAN, id, data);
}

function deletePelanggan(id) {
  return deleteData(SHEETS.PELANGGAN, id);
}

// ===== CRUD OPERATIONS - METODE PEMBAYARAN =====

function getAllMetodePembayaran() {
  return getAllData(SHEETS.METODE_PEMBAYARAN);
}

function createMetodePembayaran(data) {
  const newMetode = {
    metode_pembayaran_id: getNextId(SHEETS.METODE_PEMBAYARAN),
    nama_metode_pembayaran: data.nama_metode_pembayaran,
    keterangan: data.keterangan || ''
  };
  return insertData(SHEETS.METODE_PEMBAYARAN, newMetode);
}

function updateMetodePembayaran(id, data) {
  return updateData(SHEETS.METODE_PEMBAYARAN, id, data);
}

function deleteMetodePembayaran(id) {
  return deleteData(SHEETS.METODE_PEMBAYARAN, id);
}

// ===== SISTEM TRANSAKSI PENJUALAN =====

/**
 * Generate nomor transaksi
 */
function generateNomorTransaksi() {
  const now = new Date();
  const year = now.getFullYear().toString().substr(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const time = now.getTime().toString().substr(-6);

  return `TRX${year}${month}${day}${time}`;
}

/**
 * Membuat transaksi penjualan baru
 */
function createPenjualan(transaksiData) {
  try {
    const now = new Date();
    const penjualanId = getNextId(SHEETS.PENJUALAN);

    // Data penjualan utama
    const penjualan = {
      penjualan_id: penjualanId,
      nomor_transaksi: generateNomorTransaksi(),
      tanggal_transaksi: Utilities.formatDate(now, Session.getScriptTimeZone(), 'yyyy-MM-dd'),
      user_id: transaksiData.user_id,
      pelanggan_id: transaksiData.pelanggan_id || '',
      subtotal: transaksiData.subtotal,
      diskon_persen: transaksiData.diskon_persen || 0,
      diskon_nominal: transaksiData.diskon_nominal || 0,
      pajak_persen: transaksiData.pajak_persen || 0,
      pajak_nominal: transaksiData.pajak_nominal || 0,
      total_bayar: transaksiData.total_bayar,
      metode_pembayaran_id: transaksiData.metode_pembayaran_id,
      jumlah_cash: transaksiData.jumlah_cash || 0,
      jumlah_card: transaksiData.jumlah_card || 0,
      jumlah_digital: transaksiData.jumlah_digital || 0,
      kembalian: transaksiData.kembalian || 0,
      catatan: transaksiData.catatan || '',
      jam: Utilities.formatDate(now, Session.getScriptTimeZone(), 'HH:mm:ss'),
      tanggal: Utilities.formatDate(now, Session.getScriptTimeZone(), 'yyyy-MM-dd')
    };

    // Insert penjualan utama
    const resultPenjualan = insertData(SHEETS.PENJUALAN, penjualan);
    if (!resultPenjualan.success) {
      return resultPenjualan;
    }

    // Insert item-item penjualan dan update stok
    for (const item of transaksiData.items) {
      const penjualanItem = {
        penjualan_item_id: getNextId(SHEETS.PENJUALAN_ITEM),
        penjualan_id: penjualanId,
        barang_id: item.barang_id,
        harga_beli: item.harga_beli,
        harga_jual: item.harga_jual,
        quantity: item.quantity,
        diskon_item_persen: item.diskon_item_persen || 0,
        diskon_item_nominal: item.diskon_item_nominal || 0,
        subtotal_item: item.subtotal_item
      };

      const resultItem = insertData(SHEETS.PENJUALAN_ITEM, penjualanItem);
      if (!resultItem.success) {
        return { success: false, message: `Error menambah item: ${resultItem.message}` };
      }

      // Update stok barang
      const stokResult = updateStokBarang(item.barang_id, item.quantity);
      if (!stokResult.success) {
        return { success: false, message: `Error update stok: ${stokResult.message}` };
      }
    }

    return {
      success: true,
      message: 'Transaksi berhasil disimpan',
      penjualan_id: penjualanId,
      nomor_transaksi: penjualan.nomor_transaksi
    };

  } catch (error) {
    console.error('Error creating penjualan:', error);
    return { success: false, message: error.message };
  }
}

/**
 * Mendapatkan detail penjualan beserta item-itemnya
 */
function getPenjualanDetail(penjualanId) {
  try {
    const penjualan = findData(SHEETS.PENJUALAN, { penjualan_id: penjualanId });
    if (penjualan.length === 0) {
      return { success: false, message: 'Transaksi tidak ditemukan' };
    }

    const items = findData(SHEETS.PENJUALAN_ITEM, { penjualan_id: penjualanId });

    return {
      success: true,
      data: {
        penjualan: penjualan[0],
        items: items
      }
    };
  } catch (error) {
    console.error('Error getting penjualan detail:', error);
    return { success: false, message: error.message };
  }
}

// ===== WEB APP FUNCTIONS =====

/**
 * Fungsi utama untuk menjalankan web app
 */
function doGet(e) {
  const page = e.parameter.page || 'login';

  switch (page) {
    case 'login':
      return HtmlService.createTemplateFromFile('login').evaluate()
        .setTitle('POS UMKM - Login')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);

    case 'dashboard':
      return HtmlService.createTemplateFromFile('dashboard').evaluate()
        .setTitle('POS UMKM - Dashboard')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);

    case 'kasir':
      return HtmlService.createTemplateFromFile('kasir').evaluate()
        .setTitle('POS UMKM - Kasir')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);

    default:
      return HtmlService.createTemplateFromFile('login').evaluate()
        .setTitle('POS UMKM - Login')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
  }
}

/**
 * Handle POST requests (untuk AJAX calls)
 */
function doPost(e) {
  try {
    const data = JSON.parse(e.postData.contents);
    const action = data.action;

    switch (action) {
      case 'login':
        return ContentService.createTextOutput(JSON.stringify(verifyLogin(data.username, data.password)))
          .setMimeType(ContentService.MimeType.JSON);

      case 'getBarangByBarcode':
        return ContentService.createTextOutput(JSON.stringify(getBarangByBarcode(data.barcode)))
          .setMimeType(ContentService.MimeType.JSON);

      case 'createPenjualan':
        return ContentService.createTextOutput(JSON.stringify(createPenjualan(data.transaksi)))
          .setMimeType(ContentService.MimeType.JSON);

      // CRUD operations
      case 'getAllKategori':
        return ContentService.createTextOutput(JSON.stringify(getAllKategori()))
          .setMimeType(ContentService.MimeType.JSON);

      case 'createKategori':
        return ContentService.createTextOutput(JSON.stringify(createKategori(data.kategori)))
          .setMimeType(ContentService.MimeType.JSON);

      case 'updateKategori':
        return ContentService.createTextOutput(JSON.stringify(updateKategori(data.id, data.kategori)))
          .setMimeType(ContentService.MimeType.JSON);

      case 'deleteKategori':
        return ContentService.createTextOutput(JSON.stringify(deleteKategori(data.id)))
          .setMimeType(ContentService.MimeType.JSON);

      // Add more CRUD endpoints as needed

      default:
        return ContentService.createTextOutput(JSON.stringify({ success: false, message: 'Action tidak dikenali' }))
          .setMimeType(ContentService.MimeType.JSON);
    }
  } catch (error) {
    console.error('Error in doPost:', error);
    return ContentService.createTextOutput(JSON.stringify({ success: false, message: error.message }))
      .setMimeType(ContentService.MimeType.JSON);
  }
}

/**
 * Include file untuk HTML templates
 */
function include(filename) {
  return HtmlService.createHtmlOutputFromFile(filename).getContent();
}

// ===== UTILITY FUNCTIONS =====

/**
 * Format currency untuk tampilan
 */
function formatCurrency(amount) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0
  }).format(amount);
}

/**
 * Setup initial data (jalankan sekali untuk setup awal)
 */
function setupInitialData() {
  try {
    // Create default admin user
    const adminResult = createUser({
      username: 'admin',
      password: 'admin123',
      nama_lengkap: 'Administrator',
      role: 'administrator',
      keterangan: 'Default admin user'
    });

    // Create default kasir user
    const kasirResult = createUser({
      username: 'kasir',
      password: 'kasir123',
      nama_lengkap: 'Kasir',
      role: 'kasir',
      keterangan: 'Default kasir user'
    });

    // Create default metode pembayaran
    const metodeCash = createMetodePembayaran({
      nama_metode_pembayaran: 'Cash',
      keterangan: 'Pembayaran tunai'
    });

    const metodeCard = createMetodePembayaran({
      nama_metode_pembayaran: 'Card',
      keterangan: 'Pembayaran kartu debit/kredit'
    });

    const metodeDigital = createMetodePembayaran({
      nama_metode_pembayaran: 'Digital Wallet',
      keterangan: 'Pembayaran digital (OVO, GoPay, dll)'
    });

    console.log('Setup completed:', { adminResult, kasirResult, metodeCash, metodeCard, metodeDigital });

  } catch (error) {
    console.error('Error in setup:', error);
  }
}