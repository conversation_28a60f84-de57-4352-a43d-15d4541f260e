<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS UMKM - Test Connection</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>POS UMKM - Test Connection</h3>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <button class="btn btn-primary" onclick="testConnection()">Test Connection</button>
                            <button class="btn btn-success" onclick="testLogin()">Test Login</button>
                            <button class="btn btn-info" onclick="setupData()">Setup Initial Data</button>
                        </div>
                        
                        <div id="results" class="mt-4">
                            <h5>Results:</h5>
                            <pre id="output" class="bg-light p-3" style="min-height: 200px;"></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message) {
            const output = document.getElementById('output');
            output.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }

        async function testConnection() {
            log('Testing connection...');
            
            try {
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'test'
                    })
                });
                
                log('Response status: ' + response.status);
                
                const responseText = await response.text();
                log('Raw response: ' + responseText);
                
                const result = JSON.parse(responseText);
                log('Parsed result: ' + JSON.stringify(result, null, 2));
                
            } catch (error) {
                log('Error: ' + error.message);
                console.error('Full error:', error);
            }
        }

        async function testLogin() {
            log('Testing login...');
            
            try {
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'login',
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                log('Response status: ' + response.status);
                
                const responseText = await response.text();
                log('Raw response: ' + responseText);
                
                const result = JSON.parse(responseText);
                log('Login result: ' + JSON.stringify(result, null, 2));
                
            } catch (error) {
                log('Error: ' + error.message);
                console.error('Full error:', error);
            }
        }

        async function setupData() {
            log('Setting up initial data...');
            
            try {
                // Call setupInitialData function directly via Google Apps Script
                log('Please run setupInitialData() function manually in Google Apps Script editor');
                log('Go to script.google.com, open your project, and run the setupInitialData function');
                
            } catch (error) {
                log('Error: ' + error.message);
                console.error('Full error:', error);
            }
        }

        // Auto test on load
        window.addEventListener('load', function() {
            log('Page loaded. Ready for testing.');
            log('Current URL: ' + window.location.href);
        });
    </script>
</body>
</html>
